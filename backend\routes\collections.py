from fastapi import APIRouter, HTTPException, status
from pydantic import BaseModel
import json
import os
import logging
import shutil
from typing import List, Dict, Optional, Any

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Get the backend directory
BACKEND_DIR = os.path.abspath(os.path.join(os.path.dirname(__file__), ".."))
CAMERA_JSON_PATH = os.path.join(BACKEND_DIR, "data/camera_configuration.json")

# Create router
router = APIRouter(prefix="/api/collections", tags=["collections"])

# Define models
class Camera(BaseModel):
    ip: str
    streamUrl: str

class Collection(BaseModel):
    name: str
    cameras: Optional[Dict[str, str]] = {}

class CollectionUpdate(BaseModel):
    name: str

class CameraAdd(BaseModel):
    ip: str
    streamUrl: str

# Helper function to ensure the camera configuration file exists
def ensure_camera_config():
    if not os.path.exists(CAMERA_JSON_PATH):
        logger.info(f"Creating new camera configuration file at {CAMERA_JSON_PATH}")
        with open(CAMERA_JSON_PATH, "w") as f:
            json.dump({}, f, indent=2)
    return CAMERA_JSON_PATH

# Helper function to read the camera configuration
def read_camera_config():
    ensure_camera_config()
    try:
        with open(CAMERA_JSON_PATH, "r") as f:
            return json.load(f)
    except json.JSONDecodeError:
        logger.error(f"Error parsing camera_configuration.json")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Malformed camera_configuration.json"
        )
    except Exception as e:
        logger.error(f"Error reading camera configuration: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error reading camera configuration: {str(e)}"
        )

# Helper function to write the camera configuration
def write_camera_config(config):
    # Backup the old file
    if os.path.exists(CAMERA_JSON_PATH):
        backup_path = CAMERA_JSON_PATH + ".bak"
        try:
            shutil.copy2(CAMERA_JSON_PATH, backup_path)
            logger.debug(f"Created backup at: {backup_path}")
        except Exception as e:
            logger.error(f"Failed to backup camera_configuration.json: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to backup configuration file"
            )

    # Write to a temp file first
    temp_path = CAMERA_JSON_PATH + ".tmp"
    try:
        with open(temp_path, "w") as f:
            json.dump(config, f, indent=2)
        os.replace(temp_path, CAMERA_JSON_PATH)
        logger.debug(f"Wrote configuration to: {CAMERA_JSON_PATH}")
    except Exception as e:
        logger.error(f"Failed to write updated configuration: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to write updated configuration"
        )

# Get all collections
@router.get("/")
async def get_collections():
    try:
        camera_data = read_camera_config()
        collections = list(camera_data.keys())
        return {"collections": collections}
    except Exception as e:
        logger.error(f"Error getting collections: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e)
        )

# Get a specific collection
@router.get("/{collection_name}")
async def get_collection(collection_name: str):
    try:
        camera_data = read_camera_config()

        if collection_name not in camera_data:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Collection '{collection_name}' not found"
            )

        return {
            "name": collection_name,
            "cameras": camera_data[collection_name]
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting collection: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e)
        )

# Create a new collection
@router.post("/")
async def create_collection(collection: Collection):
    try:
        camera_data = read_camera_config()

        if collection.name in camera_data:
            raise HTTPException(
                status_code=status.HTTP_409_CONFLICT,
                detail=f"Collection '{collection.name}' already exists"
            )

        # Add the new collection
        camera_data[collection.name] = collection.cameras or {}

        # Save the updated configuration
        write_camera_config(camera_data)

        return {
            "message": f"Collection '{collection.name}' created successfully",
            "collection": {
                "name": collection.name,
                "cameras": camera_data[collection.name]
            }
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error creating collection: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e)
        )

# Update a collection (rename)
@router.put("/{collection_name}")
async def update_collection(collection_name: str, collection_update: CollectionUpdate):
    try:
        camera_data = read_camera_config()

        if collection_name not in camera_data:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Collection '{collection_name}' not found"
            )

        if collection_update.name in camera_data and collection_update.name != collection_name:
            raise HTTPException(
                status_code=status.HTTP_409_CONFLICT,
                detail=f"Collection '{collection_update.name}' already exists"
            )

        # Rename the collection
        camera_data[collection_update.name] = camera_data.pop(collection_name)

        # Save the updated configuration
        write_camera_config(camera_data)

        return {
            "message": f"Collection renamed from '{collection_name}' to '{collection_update.name}' successfully",
            "collection": {
                "name": collection_update.name,
                "cameras": camera_data[collection_update.name]
            }
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating collection: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e)
        )

# Delete a collection
@router.delete("/{collection_name}")
async def delete_collection(collection_name: str):
    try:
        camera_data = read_camera_config()

        if collection_name not in camera_data:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Collection '{collection_name}' not found"
            )

        # Remove the collection
        del camera_data[collection_name]

        # Save the updated configuration
        write_camera_config(camera_data)

        return {
            "message": f"Collection '{collection_name}' deleted successfully"
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting collection: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e)
        )

# Add a camera to a collection
@router.post("/{collection_name}/cameras")
async def add_camera_to_collection(collection_name: str, camera: CameraAdd):
    try:
        camera_data = read_camera_config()

        if collection_name not in camera_data:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Collection '{collection_name}' not found"
            )

        # Add the camera to the collection
        camera_data[collection_name][camera.ip] = camera.streamUrl

        # Save the updated configuration
        write_camera_config(camera_data)

        return {
            "message": f"Camera '{camera.ip}' added to collection '{collection_name}' successfully",
            "camera": {
                "ip": camera.ip,
                "streamUrl": camera.streamUrl
            }
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error adding camera to collection: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e)
        )

# Update a camera in a collection
@router.put("/{collection_name}/cameras/{camera_ip}")
async def update_camera_in_collection(collection_name: str, camera_ip: str, camera: CameraAdd):
    try:
        camera_data = read_camera_config()

        if collection_name not in camera_data:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Collection '{collection_name}' not found"
            )

        if camera_ip not in camera_data[collection_name]:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Camera '{camera_ip}' not found in collection '{collection_name}'"
            )

        # Update the camera in the collection
        camera_data[collection_name][camera_ip] = camera.streamUrl

        # Save the updated configuration
        write_camera_config(camera_data)

        return {
            "message": f"Camera '{camera_ip}' updated in collection '{collection_name}' successfully",
            "camera": {
                "ip": camera_ip,
                "streamUrl": camera.streamUrl
            }
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating camera in collection: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e)
        )

# Remove a camera from a collection
@router.delete("/{collection_name}/cameras/{camera_ip}")
async def remove_camera_from_collection(collection_name: str, camera_ip: str):
    try:
        camera_data = read_camera_config()

        if collection_name not in camera_data:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Collection '{collection_name}' not found"
            )

        if camera_ip not in camera_data[collection_name]:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Camera '{camera_ip}' not found in collection '{collection_name}'"
            )

        # Remove the camera from the collection
        del camera_data[collection_name][camera_ip]

        # Save the updated configuration
        write_camera_config(camera_data)

        return {
            "message": f"Camera '{camera_ip}' removed from collection '{collection_name}' successfully"
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error removing camera from collection: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e)
        )
