<!DOCTYPE html>
<html>
<head>
    <title>WebRTC RTSP Stream Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f0f0f0;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        #videoContainer {
            margin-top: 20px;
        }
        video {
            width: 100%;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .error {
            color: #d32f2f;
            background-color: #ffebee;
            padding: 10px;
            border-radius: 4px;
            margin-top: 10px;
        }
        .success {
            color: #2e7d32;
            background-color: #e8f5e8;
            padding: 10px;
            border-radius: 4px;
            margin-top: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>WebRTC RTSP Stream Test</h1>

        <div class="form-group">
            <label for="collectionName">Collection Name:</label>
            <input type="text" id="collectionName" value="k1">
        </div>

        <div class="form-group">
            <label for="cameraIp">Camera IP:</label>
            <input type="text" id="cameraIp" value="*************">
        </div>

        <button id="startButton" onclick="startStream()">Start Stream</button>

        <div id="videoContainer">
            <video id="videoElement" autoplay playsinline controls></video>
        </div>

        <div id="statusContainer"></div>
    </div>

    <script>
        let pc = null;

        function showStatus(message, isError = false) {
            const container = document.getElementById('statusContainer');
            container.innerHTML = `<div class="${isError ? 'error' : 'success'}">${message}</div>`;
        }

        async function startStream() {
            const collectionName = document.getElementById('collectionName').value;
            const cameraIp = document.getElementById('cameraIp').value;
            const videoElement = document.getElementById('videoElement');
            const startButton = document.getElementById('startButton');

            if (!collectionName || !cameraIp) {
                showStatus('Please enter both collection name and camera IP', true);
                return;
            }

            try {
                startButton.disabled = true;
                showStatus('Connecting to camera...');

                // Close existing connection if any
                if (pc) {
                    pc.close();
                }

                // Create new RTCPeerConnection
                pc = new RTCPeerConnection();

                pc.ontrack = function (event) {
                    console.log('Received track:', event);
                    videoElement.srcObject = event.streams[0];
                    showStatus('Stream connected successfully!');
                    startButton.disabled = false;
                };

                pc.oniceconnectionstatechange = function() {
                    console.log('ICE connection state:', pc.iceConnectionState);
                    if (pc.iceConnectionState === 'failed') {
                        showStatus('Connection failed', true);
                        startButton.disabled = false;
                    }
                };

                pc.addTransceiver('video', { direction: 'recvonly' });

                // Create offer
                const offer = await pc.createOffer();
                await pc.setLocalDescription(offer);

                // Send offer to server
                const response = await fetch('http://localhost:8000/offer', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        sdp: pc.localDescription.sdp,
                        type: pc.localDescription.type,
                        collection_name: collectionName,
                        camera_ip: cameraIp
                    })
                });

                const answer = await response.json();

                if (answer.error) {
                    throw new Error(answer.error);
                }

                // Set remote description
                await pc.setRemoteDescription(new RTCSessionDescription({
                    sdp: answer.sdp,
                    type: answer.type
                }));

                showStatus('Waiting for video stream...');

            } catch (error) {
                console.error('Error:', error);
                showStatus(`Error: ${error.message}`, true);
                startButton.disabled = false;
            }
        }

        // Handle video events
        document.getElementById('videoElement').addEventListener('loadstart', () => {
            console.log('Video loading started');
        });

        document.getElementById('videoElement').addEventListener('canplay', () => {
            console.log('Video can start playing');
            showStatus('Video stream is ready!');
        });

        document.getElementById('videoElement').addEventListener('error', (e) => {
            console.error('Video error:', e);
            showStatus('Video playback error', true);
        });
    </script>
</body>
</html>
