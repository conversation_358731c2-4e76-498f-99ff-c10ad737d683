import React, { useState, useEffect } from 'react';
import WebRTCPlayer from './WebRTCPlayer';
import DirectWebRTCPlayer from './DirectWebRTCPlayer';
import { useCameraStore } from '../../store/cameraStore';
import useRecordingStore from '../../store/recordingStore';
import './CameraCard.css';

const CameraCard = ({ camera, onCameraClick }) => {
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);
  const { toggleBookmark, isBookmarked } = useCameraStore();
  const { startRecording, stopRecording, isRecording } = useRecordingStore();

  const [isBookmarkedCamera, setIsBookmarkedCamera] = useState(isBookmarked(camera.id));
  const [isRecordingCamera, setIsRecordingCamera] = useState(isRecording(camera.id));

  // Update bookmark state when it changes in the store
  useEffect(() => {
    setIsBookmarkedCamera(isBookmarked(camera.id));
  }, [camera.id, isBookmarked]);

  // Update recording state when it changes in the store
  useEffect(() => {
    setIsRecordingCamera(isRecording(camera.id));
  }, [camera.id, isRecording]);

  const handleBookmarkToggle = (e) => {
    e.stopPropagation();
    console.log('Toggling bookmark for camera:', camera.id, camera.name);
    toggleBookmark(camera.id);
    // Update local state immediately for better UI responsiveness
    setIsBookmarkedCamera(!isBookmarkedCamera);
  };

  const handleCardClick = () => {
    if (onCameraClick) {
      onCameraClick(camera);
    }
  };

  const handlePlaybackStart = () => {
    setIsLoading(false);
    setHasError(false);
  };

  const handlePlaybackError = (errorMessage) => {
    setIsLoading(false);
    setHasError(true);
    console.error(`Playback error for camera ${camera.name}:`, errorMessage);
  };

  const handleRecordToggle = (e) => {
    e.stopPropagation();
    console.log('Toggling recording for camera:', camera.id, camera.name);

    if (isRecordingCamera) {
      stopRecording(camera.id);
    } else {
      startRecording(camera);
    }

    // Update local state immediately for better UI responsiveness
    setIsRecordingCamera(!isRecordingCamera);
  };

  return (
    <div
      className={`camera-card ${isBookmarkedCamera ? 'bookmarked' : ''}`}
      onClick={handleCardClick}
    >
      <div className="camera-card-header">
        <h3 className="camera-name">{camera.name}</h3>
        <div className="camera-controls">
          {/* Record button */}
          <button
            className={`record-button ${isRecordingCamera ? 'recording' : ''}`}
            onClick={handleRecordToggle}
            title={isRecordingCamera ? 'Stop Recording' : 'Start Recording'}
          >
            <svg
              width="20"
              height="20"
              viewBox="0 0 24 24"
              fill={isRecordingCamera ? "#FF4444" : "none"}
              stroke={isRecordingCamera ? "#FF4444" : "#FFFFFF"}
              strokeWidth="2"
            >
              <circle cx="12" cy="12" r="10"></circle>
              {!isRecordingCamera && <circle cx="12" cy="12" r="4" fill="#FFFFFF"></circle>}
            </svg>
          </button>

          {/* Bookmark button */}
          <button
            className={`bookmark-button ${isBookmarkedCamera ? 'bookmarked' : ''}`}
            onClick={handleBookmarkToggle}
            title={isBookmarkedCamera ? 'Remove Bookmark' : 'Add Bookmark'}
          >
            <svg
              width="20"
              height="20"
              viewBox="0 0 24 24"
              fill={isBookmarkedCamera ? "#FFD700" : "none"}
              stroke={isBookmarkedCamera ? "#FFD700" : "#FFFFFF"}
              strokeWidth="2"
            >
              <path d="M19 21l-7-5-7 5V5a2 2 0 0 1 2-2h10a2 2 0 0 1 2 2z"></path>
            </svg>
          </button>
        </div>
      </div>

      <div className="camera-card-content">
        {isLoading && !hasError && (
          <div className="camera-loading">
            <div className="loading-spinner"></div>
            <span>Loading stream...</span>
          </div>
        )}

        {hasError && (
          <div className="camera-error">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="#FF4444" strokeWidth="2">
              <circle cx="12" cy="12" r="10"></circle>
              <line x1="12" y1="8" x2="12" y2="12"></line>
              <line x1="12" y1="16" x2="12.01" y2="16"></line>
            </svg>
            <span>Stream unavailable</span>
          </div>
        )}

        {camera.streamUrl && camera.streamUrl.startsWith('rtsp://') ? (
          // Prioritize direct RTSP URLs for DirectWebRTCPlayer
          <DirectWebRTCPlayer
            rtspUrl={camera.streamUrl}
            onPlay={handlePlaybackStart}
            onError={handlePlaybackError}
          />
        ) : camera.collectionName && camera.ip ? (
          // Use WebRTCPlayer with collection name and camera IP (matches test_webrtc.html pattern)
          <WebRTCPlayer
            collectionName={camera.collectionName}
            cameraIp={camera.ip}
            onPlay={handlePlaybackStart}
            onError={handlePlaybackError}
          />
        ) : camera.ip ? (
          // Try to construct an RTSP URL from the camera IP if available
          <DirectWebRTCPlayer
            rtspUrl={`rtsp://service:Krnl$001@${camera.ip}/rtsp_tunnel?profile=0`}
            onPlay={handlePlaybackStart}
            onError={handlePlaybackError}
          />
        ) : camera.streamId && camera.roomId ? (
          // Use WebRTCPlayer for signaling-based streams as fallback
          <WebRTCPlayer
            streamId={camera.streamId}
            roomId={camera.roomId}
            onPlay={handlePlaybackStart}
            onError={handlePlaybackError}
          />
        ) : (
          // Fallback when no stream information is available
          <div className="camera-offline">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="#999999" strokeWidth="2">
              <path d="M10.5 15.5L3 8.5"></path>
              <path d="M21 8.5L16.5 13"></path>
              <rect x="3" y="3" width="18" height="12" rx="2"></rect>
              <path d="M7 15v2"></path>
              <path d="M17 15v2"></path>
              <path d="M7 19h10"></path>
            </svg>
            <span>Camera offline</span>
          </div>
        )}
      </div>

      <div className="camera-card-footer">
        <span className="camera-status">
          {hasError ? 'Error' : isLoading ? 'Connecting...' : 'Live'}
        </span>
        <span className="camera-ip">{camera.ip || 'Unknown IP'}</span>
      </div>
    </div>
  );
};

export default CameraCard;
