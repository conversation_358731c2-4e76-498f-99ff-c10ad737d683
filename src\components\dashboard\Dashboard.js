import React, { useState, useEffect } from 'react';
import LayoutSelector from '../layout/LayoutSelector';
import VideoGrid from './VideoGrid';
import { DragDropProvider } from '../DragDropContext';
import { CameraProvider, useCameras } from '../camera/CameraManager';
import BasicMap from '../maps/BasicMap';
import GlobalMap from '../maps/GlobalMap';
import CollectionManager from '../camera/CollectionManager';
import HLSPlayer from '../camera/HLSPlayer';
import CameraStreamView from '../camera/CameraStreamView';
import { useCameraStore } from '../../store/cameraStore';
import { API_BASE_URL } from '../../utils/apiConfig';
import { apiRequest } from '../../utils/api';
import './Dashboard.css';
import { getLayoutConfig } from '../layout/LayoutModes';
import { parseStreamUrl, generateCameraId } from '../../utils/cameraUtils';

const Dashboard = ({ currentView, showCollectionManager, setShowCollectionManager, activeTab, onViewChange }) => {
  const [currentLayout, setCurrentLayout] = useState('2x2');
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCamera, setSelectedCamera] = useState(null);
  const { getBookmarkedCameras } = useCameras();
  const { collections, activeCollection, setCameras, cameras: storedCameras } = useCameraStore();
  const [streams, setStreams] = useState([]);
  const [error, setError] = useState(null);
  const [loading, setLoading] = useState(false);

  const handleSearch = (event) => {
    setSearchQuery(event.target.value);
  };

  const handleCameraSelect = (cameraName) => {
    setSelectedCamera(cameraName);
  };

  const handleCloseCollectionManager = () => {
    setShowCollectionManager(false);
  };

  useEffect(() => {
    if (activeCollection && currentView === 'camera') {
      const collection = collections.find(c => c.id === activeCollection);
      if (collection) {
        loadStreams(collection.name);
      }
    } else {
      // Clear streams when switching to other views
      setStreams([]);
    }
  }, [activeCollection, collections, currentView]);

  // This effect updates the camera store with the current stream cameras
  // to ensure they're available for bookmarking
  useEffect(() => {
    if (streams.length > 0 && currentView === 'camera') {
      // Create camera objects with stable IDs
      const streamCameras = streams.map((streamInfo, index) => {
        // Handle both old format (string URLs) and new format (objects with stream_id and room_id)
        if (typeof streamInfo === 'string') {
          // Legacy format - string URL
          const safeStreamUrl = streamInfo || '';
          let name = `Camera ${index + 1}`;
          let ip = '';
          let collectionName = '';

          // Use the utility function to parse the stream URL
          const parsed = parseStreamUrl(safeStreamUrl);
          if (parsed.collectionName && parsed.ip) {
            collectionName = parsed.collectionName;
            ip = parsed.ip;
            name = `${collectionName} (${ip})`;
          }

          // Create a stable ID based on collection and IP using the utility function
          const stableId = ip ? generateCameraId(collectionName, ip) : `stream-${index}`;

          return {
            id: stableId,
            name: name,
            streamUrl: safeStreamUrl,
            ip: ip,
            collectionName: collectionName,
            collectionId: activeCollection
          };
        } else {
          // New format - object with WebRTC info or RTSP URL
          const ip = streamInfo.camera_ip || '';
          // Get collection name from the active collection
          const collection = collections.find(c => c.id === activeCollection);
          const collectionName = collection ? collection.name : '';
          const name = ip ? `${collectionName} (${ip})` : `Camera ${index + 1}`;

          // Create a stable ID based on collection and IP
          const stableId = ip ? generateCameraId(collectionName, ip) : `stream-${index}`;

          // Check if we have an RTSP URL directly
          if (streamInfo.rtsp_url) {
            return {
              id: stableId,
              name: name,
              ip: ip,
              collectionName: collectionName,
              streamUrl: streamInfo.rtsp_url,
              collectionId: activeCollection
            };
          } else {
            return {
              id: stableId,
              name: name,
              ip: ip,
              collectionName: collectionName,
              streamId: streamInfo.stream_id,
              roomId: streamInfo.room_id,
              collectionId: activeCollection
            };
          }
        }
      });

      // Merge with existing cameras, preserving bookmarks
      const existingCameraIds = storedCameras.map(cam => cam.id);
      const newCameras = streamCameras.filter(cam => !existingCameraIds.includes(cam.id));

      if (newCameras.length > 0) {
        console.log('Adding new cameras to store:', newCameras.length);
        setCameras([...storedCameras, ...newCameras]);
      }
    }
  }, [streams, currentView, activeCollection, setCameras, storedCameras]);

  const loadStreams = async (collectionName) => {
    try {
      setLoading(true);
      setError(null);

      // First try to get camera configuration directly
      console.log(`Loading camera configuration for collection: ${collectionName}`);
      try {
        const cameraData = await apiRequest(`/cameras`);

        if (cameraData.cameras && cameraData.cameras[collectionName]) {
          // Convert the camera configuration to an array of objects with RTSP URLs
          const rtspStreams = Object.entries(cameraData.cameras[collectionName]).map(([ip, rtspUrl]) => ({
            camera_ip: ip,
            rtsp_url: rtspUrl
          }));

          console.log(`Loaded ${rtspStreams.length} RTSP URLs directly from camera configuration`);
          setStreams(rtspStreams);
          setLoading(false);
          return;
        }
      } catch (cameraErr) {
        console.warn("Failed to load camera configuration directly:", cameraErr);
        // Continue to try WebRTC streams as fallback
      }

      // Fallback to WebRTC streams
      console.log(`Falling back to WebRTC streams for collection: ${collectionName}`);
      const data = await apiRequest(`/webrtc-streams/${collectionName}`);

      console.log("WebRTC stream response:", data);

      if (data.error) {
        throw new Error(data.error);
      }

      if (!data.streams || data.streams.length === 0) {
        console.warn("No streams returned from backend");
        setStreams([]);
        return;
      }

      console.log(`Received ${data.streams.length} WebRTC streams:`, data.streams);
      setStreams(data.streams);
    } catch (err) {
      console.error("Error loading streams:", err);
      setError(err.message);
      setStreams([]);
    } finally {
      setLoading(false);
    }
  };

  const renderContent = () => {
    // First check for map views
    if (currentView === 'basic-map') {
      return (
        <div className="map-container">
          <BasicMap onCameraSelect={handleCameraSelect} />
        </div>
      );
    }

    if (currentView === 'global-map') {
      return (
        <div className="map-container">
          <GlobalMap onCameraSelect={handleCameraSelect} />
        </div>
      );
    }

    // Check for the new rtsp-stream view
    if (currentView === 'rtsp-stream') {
      return <CameraStreamView />;
    }

    // Then check for loading and streams
    if (loading) {
      return <div className="loading">Loading streams...</div>;
    }

    if (error) {
      return <div className="error">{error}</div>;
    }

    if (streams.length > 0) {
      // Remove this block: always use VideoGrid for the collection view
      // (No more <div className="streams-grid">...)
    }

    switch (currentView) {
      case 'camera':
        // Prepare camera objects for VideoGrid
        const streamCameras = streams.map((streamInfo, index) => {
          // Handle both old format (string URLs) and new format (objects with stream_id and room_id)
          if (typeof streamInfo === 'string') {
            // Legacy format - string URL
            const safeStreamUrl = streamInfo || '';
            let name = `Camera ${index + 1}`;
            let ip = '';
            let collectionName = '';

            // Use the utility function to parse the stream URL
            const parsed = parseStreamUrl(safeStreamUrl);
            if (parsed.collectionName && parsed.ip) {
              collectionName = parsed.collectionName;
              ip = parsed.ip;
              name = `${collectionName} (${ip})`;
            }

            // Create a stable ID based on collection and IP using the utility function
            const stableId = ip ? generateCameraId(collectionName, ip) : `stream-${index}`;

            return {
              id: stableId,
              name: name,
              streamUrl: safeStreamUrl,
              ip: ip,
              collectionName: collectionName,
              collectionId: activeCollection
            };
          } else {
            // New format - object with WebRTC info or RTSP URL
            const ip = streamInfo.camera_ip || '';
            // Get collection name from the active collection
            const collection = collections.find(c => c.id === activeCollection);
            const collectionName = collection ? collection.name : '';
            const name = ip ? `${collectionName} (${ip})` : `Camera ${index + 1}`;

            // Create a stable ID based on collection and IP
            const stableId = ip ? generateCameraId(collectionName, ip) : `stream-${index}`;

            // Check if we have an RTSP URL directly
            if (streamInfo.rtsp_url) {
              return {
                id: stableId,
                name: name,
                ip: ip,
                collectionName: collectionName,
                streamUrl: streamInfo.rtsp_url,
                collectionId: activeCollection
              };
            } else {
              return {
                id: stableId,
                name: name,
                ip: ip,
                collectionName: collectionName,
                streamId: streamInfo.stream_id,
                roomId: streamInfo.room_id,
                collectionId: activeCollection
              };
            }
          }
        });
        return (
          <div className="dashboard-content">
            <div className="dashboard-main">
              {activeCollection ? (
                <DragDropProvider>
                  <CameraProvider>
                    <VideoGrid
                      layout={currentLayout}
                      cameras={streamCameras}
                      cellCount={getLayoutConfig(currentLayout)?.cameraCount || 4}
                      onCameraClick={handleCameraSelect}
                      activeTab={activeTab || currentView}
                    />
                  </CameraProvider>
                </DragDropProvider>
              ) : (
                <div className="no-collection-message">
                  Please select a collection from the sidebar to view cameras
                </div>
              )}
            </div>
          </div>
        );
      case 'bookmark':
        const bookmarkedCameras = getBookmarkedCameras();
        return (
          <div className="dashboard-content">
            <div className="dashboard-main">
              <DragDropProvider>
                <CameraProvider>
                  <VideoGrid
                    layout={currentLayout}
                    cameras={bookmarkedCameras}
                    cellCount={getLayoutConfig(currentLayout)?.cameraCount || 4}
                    onCameraClick={handleCameraSelect}
                    activeTab={activeTab || currentView}
                  />
                </CameraProvider>
              </DragDropProvider>
              {bookmarkedCameras.length === 0 && (
                <div className="no-bookmarks-message">
                  <p>You haven't bookmarked any cameras yet.</p>
                  <p>Click the bookmark icon on any camera to add it to your bookmarks.</p>
                </div>
              )}
            </div>
          </div>
        );
      default:
        return <div className="no-streams">No streams available</div>;
    }
  };

  return (
    <div className="dashboard">
      {showCollectionManager ? (
        <CollectionManager onClose={handleCloseCollectionManager} onViewChange={onViewChange} />
      ) : (
        <>
          <div className="dashboard-header">
            {currentView === 'camera' && (
              <div className="camera-search-container">
                <input
                  type="text"
                  placeholder="Search camera"
                  className="camera-search-input"
                  value={searchQuery}
                  onChange={handleSearch}
                />
              </div>
            )}
            {(currentView === 'camera' || currentView === 'bookmark') && (
              <div className="layout-controls">
                <LayoutSelector
                  currentLayoutId={currentLayout}
                  onLayoutChange={setCurrentLayout}
                />
              </div>
            )}
          </div>
          <div className="dashboard-content">
            {renderContent()}
          </div>
        </>
      )}
    </div>
  );
};

export default Dashboard;