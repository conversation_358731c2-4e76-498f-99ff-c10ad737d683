import React, { useEffect, useRef, useState } from "react";
import { getAugmentUrl } from '../../utils/apiConfig';
import "./CameraStream.css";

const WebRTCPlayer = ({
  collectionName,
  cameraIp,
  streamId,
  roomId,
  onError,
  onPlay
}) => {
  const videoRef = useRef(null);
  const [isConnecting, setIsConnecting] = useState(true);
  const [error, setError] = useState(null);
  const [status, setStatus] = useState('Connecting to camera...');
  const pcRef = useRef(null);

  useEffect(() => {
    // Support both prop patterns: (collectionName, cameraIp) and (streamId, roomId)
    const hasCollectionProps = collectionName && cameraIp;
    const hasStreamProps = streamId && roomId;

    if (!hasCollectionProps && !hasStreamProps) {
      setError("Missing required props: either (collectionName, cameraIp) or (streamId, roomId)");
      setIsConnecting(false);
      return;
    }

    const startStream = async () => {
      try {
        setIsConnecting(true);
        setError(null);
        setStatus('Connecting to camera...');

        // Close existing connection if any
        if (pcRef.current) {
          pcRef.current.close();
        }

        // Create new RTCPeerConnection
        const pc = new RTCPeerConnection();
        pcRef.current = pc;

        // Set up event handlers
        pc.ontrack = function (event) {
          console.log('Received track:', event);
          if (videoRef.current) {
            videoRef.current.srcObject = event.streams[0];
            setStatus('Stream connected successfully!');
            setIsConnecting(false);
            if (onPlay) onPlay();
          }
        };

        pc.oniceconnectionstatechange = function() {
          console.log('ICE connection state:', pc.iceConnectionState);
          if (pc.iceConnectionState === 'connecting') {
            setStatus('Establishing connection...');
          } else if (pc.iceConnectionState === 'connected') {
            setStatus('Connected');
          } else if (pc.iceConnectionState === 'completed') {
            setStatus('Connection established');
          } else if (pc.iceConnectionState === 'failed') {
            setError('Connection failed');
            setIsConnecting(false);
            if (onError) onError('Connection failed');
          } else if (pc.iceConnectionState === 'disconnected') {
            setStatus('Connection lost');
          } else if (pc.iceConnectionState === 'closed') {
            setStatus('Connection closed');
          }
        };

        pc.addTransceiver('video', { direction: 'recvonly' });

        // Create offer
        const offer = await pc.createOffer();
        await pc.setLocalDescription(offer);

        setStatus('Sending connection request...');

        // Send offer to server - handle both prop patterns
        let response, answer;

        if (hasCollectionProps) {
          // Use the augment API endpoint for collection/camera IP pattern
          response = await fetch(getAugmentUrl("stream"), {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({
              sdp: pc.localDescription.sdp,
              type: pc.localDescription.type,
              collection_name: collectionName,
              camera_ip: cameraIp
            })
          });

          answer = await response.json();

          if (!answer.success) {
            throw new Error(answer.error || 'Failed to establish WebRTC connection');
          }

          // Set remote description
          await pc.setRemoteDescription(new RTCSessionDescription({
            sdp: answer.data.sdp,
            type: answer.data.type
          }));
        } else if (hasStreamProps) {
          // For streamId/roomId pattern, we'll need to implement a different approach
          // This might require Socket.IO or a different endpoint
          throw new Error('StreamId/RoomId pattern not yet implemented in updated WebRTCPlayer');
        }

        setStatus('Waiting for video stream...');

      } catch (error) {
        console.error('WebRTC error:', error);
        setError(error.message);
        setIsConnecting(false);
        if (onError) onError(error.message);
      }
    };

    startStream();

    return () => {
      if (pcRef.current) {
        pcRef.current.close();
        pcRef.current = null;
      }
    };
  }, [collectionName, cameraIp, streamId, roomId, onError, onPlay]);

  // Handle video events
  const handleVideoLoadStart = () => {
    console.log('Video loading started');
    setStatus('Video loading...');
  };

  const handleVideoCanPlay = () => {
    console.log('Video can start playing');
    setStatus('Video stream is ready!');
    setIsConnecting(false);
  };

  const handleVideoError = (e) => {
    console.error('Video error:', e);
    setError('Video playback error');
    setIsConnecting(false);
    if (onError) onError('Video playback error');
  };

  return (
    <div className="camera-stream-container">
      {error ? (
        <div className="error-message">
          {error}
          <button className="retry-button" onClick={() => window.location.reload()}>
            Retry
          </button>
        </div>
      ) : isConnecting ? (
        <div className="loading-overlay">
          <div className="loading-spinner"></div>
          <div>{status}</div>
        </div>
      ) : (
        <video
          ref={videoRef}
          autoPlay
          playsInline
          controls
          style={{ width: '100%', height: '100%' }}
          onLoadStart={handleVideoLoadStart}
          onCanPlay={handleVideoCanPlay}
          onError={handleVideoError}
        />
      )}
    </div>
  );
};

export default WebRTCPlayer;